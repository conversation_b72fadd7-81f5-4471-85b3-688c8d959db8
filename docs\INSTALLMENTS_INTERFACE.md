# Loan Installments Interface Documentation

## Overview

This document describes the implementation of the loan installments interface that replaces the previous Permission interface in the Transaction page.

## Components Created/Updated

### 1. TypeScript Interface (`resources/js/types/index.d.ts`)

```typescript
export interface LoanInstallment {
    id: number;
    uuid: string;
    code: string;
    loan_id: number;
    loan?: {
        id: number;
        uuid: string;
        code: string;
        status: number;
    };
    pay_date: string;
    pay_date_formatted: string;
    due_date: string;
    due_date_formatted: string;
    tenure: number;
    total_amount: string;
    principle_amount: string;
    interest_amount: string;
    outstanding_balance_amount: string;
    status: number;
    status_label: string;
    is_overdue: boolean;
    days_overdue: number;
    created_at: string;
    updated_at: string;
    created_by?: {
        id: number;
        name: string;
    };
    updated_by?: {
        id: number;
        name: string;
    };
}
```

### 2. Backend Resource (`app/Http/Resources/Loans/LoanInstallmentResource.php`)

The `LoanInstallmentResource` transforms `LoanInstallment` model data for API responses with:
- All installment fields
- Computed status labels
- Overdue calculations
- Formatted dates
- Currency formatting support

### 3. Updated Transaction Page (`resources/js/Pages/loans/Transaction.vue`)

#### Key Features:
- **Installment Summary Cards**: Shows total, paid, unpaid, and overdue installments
- **Financial Summary**: Displays total amount, paid amount, and outstanding amount
- **Installment Schedule Table**: Comprehensive table with all installment details
- **Status Badges**: Color-coded status indicators with overdue information
- **Currency Formatting**: Proper Malaysian Ringgit formatting

#### Usage Example:

```vue
<script setup lang="ts">
import type { LoanInstallment } from '@/types';
import type { PaginatedData } from '@/types/table';

interface Props {
    installments: PaginatedData<LoanInstallment>;
    loan: any;
    // ... other props
}

const props = defineProps<Props>();

// Helper function for currency formatting
const formatCurrency = (amount: string | number) => {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount;
    return new Intl.NumberFormat('en-MY', {
        style: 'currency',
        currency: 'MYR',
        minimumFractionDigits: 2,
    }).format(num);
};

// Computed summary statistics
const installmentSummary = computed(() => {
    const data = props.installments.data;
    return {
        totalInstallments: data.length,
        paidInstallments: data.filter(item => item.status === 1).length,
        overdueInstallments: data.filter(item => item.is_overdue).length,
        // ... more calculations
    };
});
</script>
```

## Backend Integration

### Controller Update (`app/Http/Controllers/Loans/LoanController.php`)

The `transactionTab` method now properly returns installments:

```php
public function transactionTab(Request $request, Loan $loan): Response
{
    $query = LoanInstallment::where('loan_id', $loan->id)->orderBy('tenure', 'asc');
    
    $installments = $this->applyPagination($query, $request, 10,
        fn ($installment) => (new LoanInstallmentResource($installment))->toArray($request));
    
    return Inertia::render('loans/Transaction', [
        'loan' => (new LoanDetailResource($loan))->toArray(request(), true),
        'installments' => $installments,
        // ... other data
    ]);
}
```

## Status Handling

### Installment Status Values:
- `0`: Unpaid
- `1`: Paid  
- `2`: Overdue
- `3`: Partially Paid

### Status Display:
- **Paid**: Green badge
- **Unpaid (not overdue)**: Yellow badge
- **Overdue**: Red badge with days overdue
- **Partially Paid**: Orange badge

## Features

### 1. Summary Cards
- Total installments count
- Paid installments count
- Unpaid installments count
- Overdue installments count

### 2. Financial Summary
- Total amount of all installments
- Total paid amount
- Outstanding amount

### 3. Installment Table
- Installment number (tenure)
- Pay date and due date
- Amount breakdown (total, principal, interest)
- Outstanding balance
- Status with overdue indicators
- Action buttons

### 4. Responsive Design
- Mobile-friendly layout
- Proper grid system for different screen sizes
- Sticky action column

## Migration Notes

### Changes Made:
1. ✅ Removed `Permission` interface usage
2. ✅ Added `LoanInstallment` interface
3. ✅ Updated Props interface
4. ✅ Replaced permission data table with installment data table
5. ✅ Added installment summary statistics
6. ✅ Implemented proper currency formatting
7. ✅ Added status badges with overdue indicators
8. ✅ Updated backend controller to return installments

### Breaking Changes:
- `permissions` prop removed from Transaction page
- `Permission` interface no longer used in Transaction context
- DataTable now expects installment data structure

## Testing

To test the installments interface:

1. Navigate to a loan's transaction page
2. Verify installment summary cards display correct counts
3. Check that the installment table shows all installments
4. Confirm status badges display correctly
5. Test overdue calculations and display
6. Verify currency formatting
7. Test pagination functionality

## Future Enhancements

Potential improvements:
- Add payment recording functionality
- Implement installment filtering and search
- Add export functionality for installment schedules
- Include payment history tracking
- Add installment modification capabilities
