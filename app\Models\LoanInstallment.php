<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;

class LoanInstallment extends BaseModel
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'code',
        'loan_id',
        'pay_date',
        'tenure',
        'total_amount',
        'principle_amount',
        'interest_amount',
        'outstanding_balance_amount',
        'due_date',
        'status',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    protected $casts = [
        'pay_date' => 'datetime',
        'due_date' => 'datetime',
        'total_amount' => 'decimal:2',
        'principle_amount' => 'decimal:2',
        'interest_amount' => 'decimal:2',
        'outstanding_balance_amount' => 'decimal:2',
    ];

    public function loan()
    {
        return $this->belongsTo(Loan::class);
    }
}
