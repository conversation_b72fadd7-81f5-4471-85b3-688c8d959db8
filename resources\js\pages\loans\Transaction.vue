<script setup lang="ts">
import FormTransactionType from '@/components/form/transaction_type/FormTransactionType.vue';
import Heading from '@/components/Heading.vue';
import FaIcon from '@/components/FaIcon.vue';
import TabsWrapper from '@/components/TabsWrapper.vue';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, router, useForm } from '@inertiajs/vue3';    
import { computed, ref } from 'vue';
import { Button } from '@/components/ui/button';        
import type { LoanInstallment } from '@/types';
import type { PaginatedData } from '@/types/table';
import Pagination from '@/components/Pagination.vue';
import ShowingEntries from '@/components/ShowingEntries.vue';
import DataTable from '@/components/datatable/DataTable.vue';

interface Props {
    installments: PaginatedData<LoanInstallment>;
    loan: any;
    transactionTypes: Array<{
        id: number;
        value: string;
    }>;
    bankAccountTypes: Array<{
        id: number;
        value: string;
    }>;
    bankTypes: Array<{
        id: number;
        value: string;
    }>;
    letterTypes: Array<{
        id: number;
        value: string;
    }>;
    from: string,
}

const props = defineProps<Props>();
console.log(props);
const activeTab = ref('general');

// Helper function to format currency
const formatCurrency = (amount: string | number) => {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount;
    return new Intl.NumberFormat('en-MY', {
        style: 'currency',
        currency: 'MYR',
        minimumFractionDigits: 2,
    }).format(num);
};

// Computed properties for installment summary
const installmentSummary = computed(() => {
    const data = props.installments.data;
    const totalInstallments = data.length;
    const paidInstallments = data.filter(item => item.status === 1).length;
    const overdueInstallments = data.filter(item => item.is_overdue).length;
    const unpaidInstallments = data.filter(item => item.status === 0).length;

    const totalAmount = data.reduce((sum, item) => sum + parseFloat(item.total_amount), 0);
    const paidAmount = data.filter(item => item.status === 1).reduce((sum, item) => sum + parseFloat(item.total_amount), 0);
    const outstandingAmount = totalAmount - paidAmount;

    return {
        totalInstallments,
        paidInstallments,
        overdueInstallments,
        unpaidInstallments,
        totalAmount,
        paidAmount,
        outstandingAmount,
    };
});
const tabItems = computed(() => [
    { label: 'General', value: 'general' },
    { label: 'Transaction', value: 'transaction' },
    { label: 'Transaction Records', value: 'transaction-record' },
]);
const statusLabel = (status: number) => {
    const labels = [
        'Draft', // 0
        'Pending Process', // 1
        'Pending Review', // 2
        'Pending Approval', // 3
        'Rejected', // 4
        'Approved', // 5
        'Customer Rejected', // 6
        'Customer Accepted', // 7
        'On-going', // 8
        'On-going (Overdue)', // 9
        'Completed', // 10
        'Cancelled', // 11
    ];
    return labels[status] ?? 'Unknown';
};
const form = useForm({
    selection_transaction_type_id: null as number | null,
    no_instalment: null as number | null,
    selection_letter_type_id: null as number | null,
    ar_serial_no: null as string | null,
    amount: null as string | null,
    payment_date: null as string | null,
    selection_payment_method_id: null as number | null,
    payment_ref_no: null as string | null,
    rebate_amount: null as string | null,
    rebate_tenure: null as number | null,
    description: null as string | null,
});

const goToLoanDetailTab = () => {
    router.visit(route(`loans.${props.from}`, props.loan.id));
};

const columns = [
    { field: 'tenure', label: 'Installment #', sortable: true, width: 'w-24' },
    { field: 'pay_date_formatted', label: 'Pay Date', sortable: true, width: 'w-32' },
    { field: 'due_date_formatted', label: 'Due Date', sortable: true, width: 'w-32' },
    { field: 'total_amount', label: 'Total Amount (RM)', sortable: true, width: 'w-32', align: 'text-right', format: (value: string) => formatCurrency(value) },
    { field: 'principle_amount', label: 'Principal (RM)', sortable: true, width: 'w-32', align: 'text-right', format: (value: string) => formatCurrency(value) },
    { field: 'interest_amount', label: 'Interest (RM)', sortable: true, width: 'w-32', align: 'text-right', format: (value: string) => formatCurrency(value) },
    { field: 'outstanding_balance_amount', label: 'Outstanding (RM)', sortable: true, width: 'w-32', align: 'text-right', format: (value: string) => formatCurrency(value) },
    { field: 'status_label', label: 'Status', sortable: true, width: 'w-24' },
    { field: 'actions', label: 'Action', sortable: false, sticky: true, width: 'w-[50px]', align: 'text-center' },
];
const handlePaginate = (url: string) => {
    form.get(url);
};
</script>

<template>
    <AppLayout>
        <Head title="Loan" />
        <div class="px-4 py-3">
            <Heading title="Loans" pageNumber="P000012" description="Edit the selected loan record">
                <template #status>
                    <Badge
                        :class="[
                            {
                                'bg-ocean': props.loan.status === 1,
                                'bg-canary': props.loan.status === 2,
                                'bg-orange': props.loan.status === 3,
                                'bg-chrome': props.loan.status === 4,
                                'bg-castleton': props.loan.status === 5,
                                'bg-pink': props.loan.status === 6,
                                'bg-soften': props.loan.status === 7,
                                'bg-cobalt': props.loan.status === 8,
                                'bg-tomato': props.loan.status === 9,
                                'bg-green': props.loan.status === 10,
                                'bg-mist': props.loan.status === 11,
                            },
                            'text-md px-1 py-0',
                        ]"
                    >
                        {{ statusLabel(props.loan.status) }}
                    </Badge>
                </template>
                <template #slot>
                    <div class="border border-gainsboro">
                        <Button type="button" @click="goToLoanDetailTab" class="bg-background px-4 py-2 text-black hover:bg-background hover:text-black"><FaIcon name="sack-dollar" class="pr-3" /> Loan Details</Button>
                        <Button type="button" @click="" class="bg-azure px-4 py-2 text-white hover:bg-azure hover:text-white"> <FaIcon name="right-left" class="pr-3" />Transaction</Button>
                    </div>
                </template>
            </Heading>
            <Card class="gap-0 py-0">
                <CardHeader class="bg-azure gap-0 rounded-t-lg px-5.5 py-3 text-white">
                    <CardTitle>Loan ID: {{ props.loan.code }}</CardTitle>
                </CardHeader>
                <TabsWrapper v-model="activeTab" :tabs="tabItems">
                    <template #general>
                        <CardContent class="px-6 py-2">
                            <Label class="text-[20px]" for="">General</Label>
                            <Label class="py-4 text-[20px]" for="">Loan Details</Label>
                            <div class="grid grid-cols-1 gap-3 lg:grid-cols-1">
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Loan Release Date</Label>
                                        <p>123</p>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Loan Type</Label>
                                        <p>{{ props.loan.type }}</p>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Loan Mode</Label>
                                        <p>{{ props.loan.loanDetail.mode_type }}</p>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Instalment Amount (RM)</Label>
                                        <p>{{ props.loan.loanDetail.instalment_amount }}</p>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Last Payment Amount (RM)</Label>
                                        <p>{{ props.loan.loanDetail.last_payment }}</p>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">No of Instalment (Tenure)</Label>
                                        <p>{{ props.loan.loanDetail.no_of_instalment }}</p>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Instalment Arrears (RM)</Label>
                                        <p>123</p>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Current Due Date</Label>
                                        <p>123</p>
                                    </div>
                                </div>
                            </div>
                            <Label class="py-4 text-[20px]" for="">Loan Balance Status</Label>
                            <div class="grid grid-cols-1 gap-3 lg:grid-cols-1">
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Loan Principle Amount</Label>
                                        <p>{{ props.loan.loanDetail.loan_principle_amount }}</p>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Interest Charges (+)</Label>
                                        <p>{{ props.loan.loanDetail.interest }}</p>
                                    </div>
                                    <Separator class="my-4" />
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base">Balance Payable (RM)</Label>
                                        <p>123</p>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Total Instalment Paid (RM)(-)</Label>
                                        <p>123</p>
                                    </div>
                                    <Separator class="my-4" />
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base">Instalment Balance (RM)</Label>
                                        <p>123</p>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Late Interest Charges (RM)(+)</Label>
                                        <p>123</p>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Postage Charges (RM)(+)</Label>
                                        <p>123</p>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Legal Fee (RM)(+)</Label>
                                        <p>123</p>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Misc Charges (RM)(+)</Label>
                                        <p>123</p>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Rebate (RM)(-)</Label>
                                        <p>123</p>
                                    </div>
                                    <Separator class="my-4" />
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base">Current Balance (RM)</Label>
                                        <p>123</p>
                                    </div>
                                </div>
                            </div>

                            <Label class="py-4 text-[20px]" for="">Installment Summary</Label>
                            <div class="grid grid-cols-2 gap-4 lg:grid-cols-4 mb-6">
                                <div class="bg-blue-50 p-4 rounded-lg">
                                    <Label class="text-sm text-gray-600">Total Installments</Label>
                                    <p class="text-2xl font-bold text-blue-600">{{ installmentSummary.totalInstallments }}</p>
                                </div>
                                <div class="bg-green-50 p-4 rounded-lg">
                                    <Label class="text-sm text-gray-600">Paid</Label>
                                    <p class="text-2xl font-bold text-green-600">{{ installmentSummary.paidInstallments }}</p>
                                </div>
                                <div class="bg-yellow-50 p-4 rounded-lg">
                                    <Label class="text-sm text-gray-600">Unpaid</Label>
                                    <p class="text-2xl font-bold text-yellow-600">{{ installmentSummary.unpaidInstallments }}</p>
                                </div>
                                <div class="bg-red-50 p-4 rounded-lg">
                                    <Label class="text-sm text-gray-600">Overdue</Label>
                                    <p class="text-2xl font-bold text-red-600">{{ installmentSummary.overdueInstallments }}</p>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 gap-4 lg:grid-cols-3 mb-6">
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <Label class="text-sm text-gray-600">Total Amount</Label>
                                    <p class="text-xl font-bold text-gray-800">{{ formatCurrency(installmentSummary.totalAmount) }}</p>
                                </div>
                                <div class="bg-green-50 p-4 rounded-lg">
                                    <Label class="text-sm text-gray-600">Paid Amount</Label>
                                    <p class="text-xl font-bold text-green-600">{{ formatCurrency(installmentSummary.paidAmount) }}</p>
                                </div>
                                <div class="bg-orange-50 p-4 rounded-lg">
                                    <Label class="text-sm text-gray-600">Outstanding Amount</Label>
                                    <p class="text-xl font-bold text-orange-600">{{ formatCurrency(installmentSummary.outstandingAmount) }}</p>
                                </div>
                            </div>

                            <Label class="py-4 text-[20px]" for="">Installment Schedule</Label>
                            <div class="mb-4">
                                <DataTable
                                    :columns="columns"
                                    :data="installments.data"
                                    empty-message="No installments found."
                                    :showDeleteButton="false"
                                    :showStatusToggle="false"
                                    :sortState="{ field: 'tenure', direction: 'asc' }"
                                >
                                    <template #status_label="{ item }">
                                        <Badge
                                            :class="[
                                                {
                                                    'bg-green text-white': item.status === 1,
                                                    'bg-red text-white': item.status === 0 && item.is_overdue,
                                                    'bg-yellow text-black': item.status === 0 && !item.is_overdue,
                                                    'bg-orange text-white': item.status === 3,
                                                },
                                                'px-2 py-1 text-xs rounded',
                                            ]"
                                        >
                                            {{ item.status_label }}
                                            <span v-if="item.is_overdue" class="ml-1">({{ item.days_overdue }} days)</span>
                                        </Badge>
                                    </template>
                                </DataTable>
                            </div>
                            <div class="bg-white">
                                <div class="flex items-center justify-between">
                                    <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                                        <div>
                                            <ShowingEntries :from="installments.from" :to="installments.to" :total="installments.total" entityName="installments" />
                                        </div>
                                        <Pagination :links="installments.links" @navigate="handlePaginate" />
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                        <!-- Installment data table implemented above -->
                    </template>
                    <template #transaction>
                        <FormTransactionType
                            :form="form"
                            :loan="props.loan"
                            :from="props.from"
                            :transactionTypes="props.transactionTypes"
                            :letterTypes="props.letterTypes"
                        />
                    </template>
                    <template #transaction-record>
                        <CardContent class="px-6 py-2">
                            <Label class="text-[20px]" for="">Transaction Records</Label>
                            <!-- ToDo: dataTAble for all paid loanDetail and able to do print -->
                        </CardContent>
                    </template>
                </TabsWrapper>
            </Card>
        </div>
    </AppLayout>
</template>
