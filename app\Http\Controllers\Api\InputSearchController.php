<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\CustomerCollateral;
use App\Models\CustomerProfile;
use App\Models\LoanDetail;
use Illuminate\Http\Request;

class InputSearchController extends Controller
{
    public function searchCustomers(Request $request)
    {
        $searchKeyword = $request->input('search');

        if (empty($searchKeyword)) {
            return response()->json([]);
        }

        $matchedCustomers = CustomerProfile::query()
            ->select('id', 'name', 'identity_no')
            ->where(function ($query) use ($searchKeyword) {
                $query->where('name', 'like', "%{$searchKeyword}%")
                    ->orWhere('identity_no', 'like', "%{$searchKeyword}%");
            })
            ->get();

        $formattedResults = $matchedCustomers->map(function ($customer) {
            return [
                'value' => $customer->id,
                'label' => [
                    'name' => $customer->name,
                    'identity_no' => $customer->identity_no,
                ],
            ];
        });

        return response()->json($formattedResults);
    }

    public function searchCollaterals(Request $request)
    {

        $search = $request->input('search');
        $customerIds = $request->input('customer_ids', []);

        $customerCollaterals = CustomerCollateral::with('collateral.property')
            ->whereIn('customer_id', $customerIds)
            ->whereHas('collateral', function ($query) use ($search) {
                $query->where('remark', 'like', "%{$search}%")
                    ->orWhereHas('property', function ($q) use ($search) {
                        $q->where('ownership_no', 'like', "%{$search}%")
                            ->orWhere('lot_number', 'like', "%{$search}%");
                    });
            })
            ->get();

        return response()->json($customerCollaterals->map(function ($customerCollateral) {
            return [
                'value' => $customerCollateral->id, // this is customer_collateral_id
                'label' => [
                    'remark' => $customerCollateral->collateral->remark ?? '-',
                    'ownership_no' => $customerCollateral->collateral->property->ownership_no ?? '-',
                    'lot_number' => $customerCollateral->collateral->property->lot_number ?? '-',
                ],
            ];
        }));
    }

    public function calculateLoan(Request $request)
    {
        $loanDetail = LoanDetail::first();

        $loanDetail->generateInstallments();

        return response()->json([
            'amount' => $loanDetail->loan_principle_amount,
            'tenure' => $loanDetail->no_of_instalment,
        ]);
    }
}
