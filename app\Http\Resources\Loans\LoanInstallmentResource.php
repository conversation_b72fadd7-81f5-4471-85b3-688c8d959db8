<?php

namespace App\Http\Resources\Loans;

use App\Enums\Loan\LoanInstallmentStatus;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * LoanInstallmentResource
 *
 * Transforms LoanInstallment model data for API responses.
 *
 * Usage examples:
 * - Single installment: new LoanInstallmentResource($installment)
 * - Collection: LoanInstallmentResource::collection($installments)
 * - With loan relationship: $installment->load('loan'); new LoanInstallmentResource($installment)
 */
class LoanInstallmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'code' => $this->code,
            'loan_id' => $this->loan_id,
            'loan' => $this->whenLoaded('loan', fn () => [
                'id' => $this->loan->id,
                'uuid' => $this->loan->uuid,
                'code' => $this->loan->code,
                'status' => $this->loan->status->value,
            ]),
            'pay_date' => $this->pay_date,
            'pay_date_formatted' => $this->pay_date?->format('Y-m-d'),
            'due_date' => $this->due_date,
            'due_date_formatted' => $this->due_date?->format('Y-m-d'),
            'tenure' => $this->tenure,
            'total_amount' => $this->total_amount,
            'principle_amount' => $this->principle_amount,
            'interest_amount' => $this->interest_amount,
            'outstanding_balance_amount' => $this->outstanding_balance_amount,
            'status' => $this->status,
            'is_overdue' => $this->due_date < now() && $this->status === LoanInstallmentStatus::UNPAID->value,
            'days_overdue' => $this->due_date < now() && $this->status === LoanInstallmentStatus::UNPAID->value
                ? now()->diffInDays($this->due_date)
                : 0,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'created_by' => $this->whenLoaded('createdBy', fn () => [
                'id' => $this->createdBy->id,
                'name' => $this->createdBy->username,
            ]),
            'updated_by' => $this->whenLoaded('updatedBy', fn () => [
                'id' => $this->updatedBy->id,
                'name' => $this->updatedBy->username,
            ]),
        ];
    }
}