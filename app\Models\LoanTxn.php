<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class LoanTxn extends BaseModel
{
    use HasFactory;

    // Scopes for prioritization
    public function scopeUnpaid($q)
    {
        return $q->where('status', 0);
    }

    public function scopeLateInterest($q)
    {
        return $q->where('loan_txn_type_id', 2);
    }

    public function scopeLegalFee($q)
    {
        return $q->where('loan_txn_type_id', 3);
    }

    public function scopePostage($q)
    {
        return $q->where('loan_txn_type_id', 5);
    }

    public function scopeMiscCharge($q)
    {
        return $q->where('loan_txn_type_id', 4);
    }

    public function scopeInstallment($q)
    {
        return $q->where('loan_txn_type_id', 1);
    }

    public function repaymentQueue(Request $request): LengthAwarePaginator
    {
        $queries = [
            LoanTxn::unpaid()->lateInterest()->where('loan_id', $this->id)->orderBy('tenure'),
            LoanTxn::unpaid()->legalFee()->where('loan_id', $this->id)->orderBy('created_at'),
            LoanTxn::unpaid()->postage()->where('loan_id', $this->id)->orderBy('tenure'),
            LoanTxn::unpaid()->miscCharge()->where('loan_id', $this->id)->orderBy('created_at'),
            LoanTxn::unpaid()->installment()->where('loan_id', $this->id)->orderBy('tenure'),
        ];

        $merged = collect();
        foreach ($queries as $query) {
            $merged = $merged->merge($query->get());
        }

        $withMeta = $merged->map(function ($txn) {
            return [
                'id' => $txn->id,
                'type' => $txn->txn_type,
                'amount' => $txn->amount,
                'tenure' => optional($txn->installment)->tenure,
                'date' => $txn->created_at,
                'model' => $txn,
            ];
        });

        $page = LengthAwarePaginator::resolveCurrentPage();
        $perPage = 10;
        $results = $withMeta->forPage($page, $perPage)->values();

        return new LengthAwarePaginator($results, $withMeta->count(), $perPage, $page, [
            'path' => $request->url(),
            'query' => $request->query(),
        ]);
    }
}
